{"name": "barcode-cafe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "ts-node src/scripts/create-admin.ts", "seed:menu-items": "ts-node --project scripts/tsconfig.json scripts/seed-menu-items.ts", "test": "jest"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.10", "@types/js-cookie": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.2", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "html2pdf.js": "^0.9.3", "jose": "^6.0.10", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.483.0", "next": "15.2.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tailwindcss": "^4", "ts-node": "^10.9.1", "typescript": "^5", "url-loader": "^4.1.1"}}