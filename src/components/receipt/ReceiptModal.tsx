'use client';

import { useState } from 'react';
import { Order } from '@/types/models';
import { useLocale } from '@/contexts/LocaleContext';
import { formatDate } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ReceiptModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function ReceiptModal({ order, isOpen, onClose }: ReceiptModalProps) {
  const { t, isClient, locale } = useLocale();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  if (!order) return null;

  // Calculate totals
  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = order.deliveryFee || 0;
  const tax = order.tax || 0;
  const total = order.total || (subtotal + deliveryFee + tax);

  // Generate receipt number from order ID
  const receiptNumber = `RC-${order.id.slice(-8).toUpperCase()}`;

  const formatOrderDate = () => {
    if (order.createdAt instanceof Date) {
      return formatDate(order.createdAt, locale, true);
    } else if (typeof order.createdAt === 'string') {
      return formatDate(new Date(order.createdAt), locale, true);
    }
    return new Date().toLocaleDateString();
  };

  const handlePrint = () => {
    // Create a new window with only the receipt content
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (!printWindow) {
      alert('Please allow popups to print the receipt');
      return;
    }

    // Format date for receipt
    const formatReceiptDate = () => {
      try {
        if (order.createdAt && order.createdAt.seconds) {
          return new Date(order.createdAt.seconds * 1000).toLocaleString();
        } else if (order.createdAt && typeof order.createdAt === 'string') {
          return new Date(order.createdAt).toLocaleString();
        } else if (order.createdAt) {
          return new Date(order.createdAt).toLocaleString();
        }
      } catch (error) {
        console.error('Error formatting date:', error);
      }
      return 'N/A';
    };

    const receiptHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${receiptNumber}</title>
          <style>
            body {
              font-family: 'Courier New', monospace;
              margin: 0;
              padding: 20px;
              background: white;
              color: black;
              font-size: 12px;
              line-height: 1.4;
              width: 80mm;
              max-width: 300px;
            }
            .receipt {
              width: 100%;
            }
            .center {
              text-align: center;
            }
            .bold {
              font-weight: bold;
            }
            .separator {
              border-top: 1px dashed #000;
              margin: 8px 0;
              width: 100%;
            }
            .item-row {
              display: flex;
              justify-content: space-between;
              margin: 2px 0;
              width: 100%;
            }
            .item-row .left {
              flex: 1;
              text-align: left;
            }
            .item-row .right {
              text-align: right;
              margin-left: 10px;
            }
            .barcode {
              font-family: 'Courier New', monospace;
              letter-spacing: 1px;
              font-size: 14px;
              margin: 5px 0;
            }
            .business-name {
              font-size: 14px;
              font-weight: bold;
              margin: 2px 0;
            }
            .business-info {
              font-size: 10px;
              margin: 1px 0;
            }
            .section-title {
              font-weight: bold;
              font-size: 11px;
              margin: 8px 0 4px 0;
            }
            .item-options {
              font-size: 10px;
              margin-left: 15px;
              color: #666;
            }
            .total-final {
              font-weight: bold;
              font-size: 13px;
              border-top: 1px solid #000;
              padding-top: 3px;
              margin-top: 4px;
            }
            @media print {
              body {
                margin: 0;
                padding: 10px;
                font-size: 11px;
              }
              .receipt {
                max-width: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            <!-- Header -->
            <div class="center">
              <div class="business-name">BARCODE CAFE</div>
              <div class="business-info">Standard Coffee House</div>
              <div class="business-info">123 Coffee Street, City Center</div>
              <div class="business-info">Tel: +965 1234 5678</div>
              <div class="business-info">www.barcodecafe.com</div>
            </div>

            <div class="separator"></div>

            <!-- Order Information -->
            <div class="section-title">ORDER INFORMATION</div>
            <div>Receipt No: ${receiptNumber}</div>
            <div>Order No: ${order.id}</div>
            <div>Date: ${formatReceiptDate()}</div>
            <div>Cashier: System</div>
            <div>Payment Method: ${order.paymentMethod}</div>

            ${order.deliveryType === 'DELIVERY' && order.deliveryAddress ? `
              <div class="separator"></div>
              <div class="section-title">DELIVERY INFO</div>
              <div>Address: ${order.deliveryAddress}</div>
            ` : ''}

            ${order.deliveryType === 'TABLE' && order.tableNumber ? `
              <div class="separator"></div>
              <div class="section-title">TABLE INFO</div>
              <div>Table Number: ${order.tableNumber}</div>
            ` : ''}

            <div class="separator"></div>

            <!-- Order Items -->
            <div class="section-title">ORDER ITEMS</div>
            ${order.items.map(item => `
              <div class="item-row">
                <span class="left">${item.quantity}x ${item.name}</span>
                <span class="right">${(item.price * item.quantity).toFixed(2)} SAR</span>
              </div>
              ${item.options && item.options.length > 0 ?
                item.options.map(option => `<div class="item-options">+ ${option.name}: ${option.value}</div>`).join('')
                : ''
              }
            `).join('')}

            ${order.specialInstructions ? `
              <div style="margin-top: 8px; font-style: italic;">
                <strong>Notes:</strong> ${order.specialInstructions}
              </div>
            ` : ''}

            <div class="separator"></div>

            <!-- Totals -->
            <div class="item-row">
              <span class="left">Subtotal:</span>
              <span class="right">${subtotal.toFixed(2)} SAR</span>
            </div>
            ${deliveryFee > 0 ? `
              <div class="item-row">
                <span class="left">Delivery Fee:</span>
                <span class="right">${deliveryFee.toFixed(2)} SAR</span>
              </div>
            ` : ''}
            ${tax > 0 ? `
              <div class="item-row">
                <span class="left">Tax:</span>
                <span class="right">${tax.toFixed(2)} SAR</span>
              </div>
            ` : ''}

            <div class="item-row total-final">
              <span class="left">TOTAL:</span>
              <span class="right">${total.toFixed(2)} SAR</span>
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="center">
              <div style="margin: 4px 0;"><strong>Thank you for your visit!</strong></div>
              <div style="margin: 2px 0;">Enjoy your meal and have a great day!</div>
              <div style="margin: 2px 0;">Follow us on social media @barcodecafe</div>
              <br>
              <div class="barcode">||||||||||||||||||||</div>
              <div>${receiptNumber}</div>
              <br>
              <div style="font-size: 9px; color: #666;">Powered by Barcode Cafe System</div>
            </div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(receiptHTML);
    printWindow.document.close();

    // Wait for content to load, then print and close
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };
  };

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true);
    try {
      // Import jsPDF dynamically
      const { jsPDF } = await import('jspdf');

      // Create a new PDF document (80mm width for receipt)
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [80, 200] // 80mm width, 200mm height
      });

      // Set font for receipt
      pdf.setFont('courier', 'normal');
      pdf.setFontSize(10);

      let yPosition = 10;
      const lineHeight = 4;
      const pageWidth = 80;
      const margin = 5;

      // Helper function to add centered text
      const addCenteredText = (text: string, fontSize = 10) => {
        pdf.setFontSize(fontSize);
        const textWidth = pdf.getTextWidth(text);
        const x = (pageWidth - textWidth) / 2;
        pdf.text(text, x, yPosition);
        yPosition += lineHeight;
      };

      // Helper function to add left-aligned text
      const addText = (text: string, fontSize = 10) => {
        pdf.setFontSize(fontSize);
        pdf.text(text, margin, yPosition);
        yPosition += lineHeight;
      };

      // Helper function to add right-aligned text
      const addRightText = (text: string, fontSize = 10) => {
        pdf.setFontSize(fontSize);
        const textWidth = pdf.getTextWidth(text);
        pdf.text(text, pageWidth - margin - textWidth, yPosition);
        yPosition += lineHeight;
      };

      // Helper function to add line with left and right text
      const addLineWithBothSides = (leftText: string, rightText: string, fontSize = 10) => {
        pdf.setFontSize(fontSize);
        pdf.text(leftText, margin, yPosition);
        const rightTextWidth = pdf.getTextWidth(rightText);
        pdf.text(rightText, pageWidth - margin - rightTextWidth, yPosition);
        yPosition += lineHeight;
      };

      // Header
      addCenteredText('BARCODE CAFE', 12);
      addCenteredText('Standard Coffee House');
      addCenteredText('123 Coffee Street, City Center');
      addCenteredText('Tel: +965 1234 5678');
      addCenteredText('www.barcodecafe.com');

      // Separator line
      yPosition += 2;
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 4;

      // Order Information
      addText('ORDER INFORMATION', 10);
      addText(`Receipt No: ${receiptNumber}`);
      addText(`Order No: ${order.id}`);

      // Format date properly
      let formattedDate = 'N/A';
      try {
        if (order.createdAt && order.createdAt.seconds) {
          formattedDate = new Date(order.createdAt.seconds * 1000).toLocaleString();
        } else if (order.createdAt && typeof order.createdAt === 'string') {
          formattedDate = new Date(order.createdAt).toLocaleString();
        } else if (order.createdAt) {
          formattedDate = new Date(order.createdAt).toLocaleString();
        }
      } catch (error) {
        console.error('Error formatting date:', error);
        formattedDate = 'N/A';
      }

      addText(`Date: ${formattedDate}`);
      addText(`Cashier: System`);
      addText(`Payment Method: ${order.paymentMethod}`);

      // Separator line
      yPosition += 2;
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 4;

      // Order Items
      addText('ORDER ITEMS', 10);
      order.items.forEach(item => {
        const itemText = `${item.quantity}x ${item.name}`;
        const priceText = `${item.price.toFixed(2)} SAR`;
        addLineWithBothSides(itemText, priceText);
      });

      // Separator line
      yPosition += 2;
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 4;

      // Totals
      addLineWithBothSides('Subtotal:', `${order.subtotal.toFixed(2)} SAR`);
      addLineWithBothSides('Delivery Fee:', `${order.deliveryFee.toFixed(2)} SAR`);

      // Total line
      yPosition += 2;
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 4;
      addLineWithBothSides('TOTAL:', `${order.total.toFixed(2)} SAR`, 12);

      // Footer
      yPosition += 8;
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 6;

      addCenteredText('Thank you for your visit!');
      addCenteredText('Enjoy your meal and have a great day!');
      addCenteredText('Follow us on social media @barcodecafe');

      yPosition += 4;
      addCenteredText('||||||||||||||||||||');
      addCenteredText(receiptNumber);

      yPosition += 4;
      addCenteredText('Powered by Barcode Cafe System', 8);

      // Save the PDF
      pdf.save(`receipt-${receiptNumber}.pdf`);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('PDF generation failed. Please try the print option instead.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-center">
            {isClient ? t('receipt.title') : 'Receipt Preview'}
          </DialogTitle>
        </DialogHeader>

        {/* Receipt Content */}
        <div id="receipt-content" className="bg-white p-6 border border-gray-200 rounded-lg">
          {/* Receipt Header */}
          <div className="header text-center mb-4 pb-3 border-b border-dashed border-gray-400">
            <div className="business-name text-lg font-bold mb-1">BARCODE CAFE</div>
            <div className="business-info text-xs text-gray-600">Standard Coffee House</div>
            <div className="business-info text-xs text-gray-600">123 Coffee Street, City Center</div>
            <div className="business-info text-xs text-gray-600">Tel: +965 1234 5678</div>
            <div className="business-info text-xs text-gray-600">www.barcodecafe.com</div>
          </div>

          {/* Order Information */}
          <div className="section order-info mb-4 pb-3 border-b border-dashed border-gray-400">
            <div className="section-title text-xs font-bold mb-2 uppercase">
              {isClient ? t('receipt.orderInfo') : 'ORDER INFORMATION'}
            </div>
            <div className="text-xs space-y-1">
              <div>{isClient ? t('receipt.receiptNo') : 'Receipt No'}: {receiptNumber}</div>
              <div>{isClient ? t('receipt.orderNo') : 'Order No'}: #{order.id.slice(-8).toUpperCase()}</div>
              <div>{isClient ? t('receipt.date') : 'Date'}: {formatOrderDate()}</div>
              <div>{isClient ? t('receipt.cashier') : 'Cashier'}: System</div>
              <div>{isClient ? t('receipt.paymentMethod') : 'Payment'}: {isClient ? t('checkout.cash') : 'Cash'}</div>
            </div>
          </div>

          {/* Customer Information */}
          {(order.deliveryType === 'DELIVERY' || order.customerName) && (
            <div className="section mb-4">
              <div className="section-title text-xs font-bold mb-2 uppercase">
                {isClient ? t('receipt.customerInfo') : 'CUSTOMER INFORMATION'}
              </div>
              <div className="text-xs space-y-1">
                {order.customerName && (
                  <div>{isClient ? t('receipt.customer') : 'Customer'}: {order.customerName}</div>
                )}
                {order.deliveryType === 'DELIVERY' && order.deliveryAddress && (
                  <div>{isClient ? t('receipt.address') : 'Address'}: {order.deliveryAddress}</div>
                )}
                {order.deliveryType === 'TABLE' && order.tableNumber && (
                  <div>{isClient ? t('receipt.table') : 'Table'}: {order.tableNumber}</div>
                )}
                {order.deliveryType === 'PICKUP' && (
                  <div>{isClient ? t('receipt.pickup') : 'Pickup Order'}</div>
                )}
              </div>
            </div>
          )}

          {/* Order Items */}
          <div className="section items mb-4 pb-3 border-b border-dashed border-gray-400">
            <div className="section-title text-xs font-bold mb-2 uppercase">
              {isClient ? t('receipt.items') : 'ORDER ITEMS'}
            </div>
            <div className="space-y-2">
              {order.items.map((item, index) => (
                <div key={index} className="item flex justify-between items-start">
                  <div className="flex-1">
                    <div className="item-name text-xs font-bold">
                      {item.quantity}x {item.name}
                    </div>
                    {item.options && item.options.length > 0 && (
                      <div className="item-options text-xs text-gray-500 italic ml-2">
                        {item.options.map((option, optIndex) => (
                          <div key={optIndex}>• {option.name}: {option.value}</div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="item-price text-xs ml-2">
                    {(item.price * item.quantity).toFixed(2)} {isClient ? t('common.currency') : 'SAR'}
                  </div>
                </div>
              ))}
              
              {order.specialInstructions && (
                <div className="mt-3 text-xs italic">
                  <strong>{isClient ? t('receipt.notes') : 'Notes'}:</strong> {order.specialInstructions}
                </div>
              )}
            </div>
          </div>

          {/* Totals */}
          <div className="section totals mb-4 pb-3 border-b border-dashed border-gray-400">
            <div className="space-y-1">
              <div className="total-line flex justify-between text-xs">
                <span>{isClient ? t('receipt.subtotal') : 'Subtotal'}:</span>
                <span>{subtotal.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
              </div>
              
              {deliveryFee > 0 && (
                <div className="total-line flex justify-between text-xs">
                  <span>{isClient ? t('receipt.deliveryFee') : 'Delivery Fee'}:</span>
                  <span>{deliveryFee.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
                </div>
              )}
              
              {tax > 0 && (
                <div className="total-line flex justify-between text-xs">
                  <span>{isClient ? t('receipt.tax') : 'Tax'}:</span>
                  <span>{tax.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
                </div>
              )}
              
              <div className="total-line final flex justify-between text-sm font-bold border-t border-gray-400 pt-2 mt-2">
                <span>{isClient ? t('receipt.total') : 'TOTAL'}:</span>
                <span>{total.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
              </div>
            </div>
          </div>

          {/* Receipt Footer */}
          <div className="footer text-center text-xs">
            <div className="footer-line mb-1">
              <strong>{isClient ? t('receipt.thankYou') : 'Thank you for your visit!'}</strong>
            </div>
            <div className="footer-line mb-1">
              {isClient ? t('receipt.enjoyMeal') : 'Enjoy your meal and have a great day!'}
            </div>
            <div className="footer-line mb-3">
              {isClient ? t('receipt.followUs') : 'Follow us on social media @barcodecafe'}
            </div>
            
            {/* Receipt Barcode */}
            <div className="barcode font-mono text-xs mb-1">
              ||||| |||| | ||| |||| |||||
            </div>
            <div className="text-xs mb-3">
              {receiptNumber}
            </div>
            
            <div className="footer-line text-xs text-gray-500">
              {isClient ? t('receipt.powered') : 'Powered by Barcode Cafe System'}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            {isClient ? t('common.close') : 'Close'}
          </Button>
          <Button 
            onClick={handleDownloadPDF}
            disabled={isGeneratingPDF}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isGeneratingPDF ? (
              <>
                <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                {isClient ? t('common.generating') : 'Generating...'}
              </>
            ) : (
              <>
                <i className="fa-solid fa-download mr-2"></i>
                {isClient ? t('receipt.downloadPDF') : 'Download PDF'}
              </>
            )}
          </Button>
          <Button 
            onClick={handlePrint}
            className="bg-[#83EAED] hover:bg-[#83EAED]/90 text-white"
          >
            <i className="fa-solid fa-print mr-2"></i>
            {isClient ? t('orders.printReceipt') : 'Print Receipt'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
