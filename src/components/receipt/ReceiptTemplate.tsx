'use client';

import { Order } from '@/types/models';
import { useLocale } from '@/contexts/LocaleContext';
import { formatDate } from '@/lib/utils';

interface ReceiptTemplateProps {
  order: Order;
  className?: string;
}

export default function ReceiptTemplate({ order, className = '' }: ReceiptTemplateProps) {
  const { t, isClient, locale } = useLocale();
  
  // Calculate totals
  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = order.deliveryFee || 0;
  const tax = order.tax || 0;
  const total = order.total || (subtotal + deliveryFee + tax);
  
  // Generate receipt number from order ID
  const receiptNumber = `RC-${order.id.slice(-8).toUpperCase()}`;
  
  return (
    <div className={`receipt-template ${className}`}>
      {/* Print-only styles */}
      <style jsx>{`
        @media print {
          .receipt-template {
            width: 80mm !important;
            max-width: 80mm !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 12px !important;
            line-height: 1.2 !important;
            color: #000 !important;
            background: white !important;
          }
          
          .receipt-header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 8px;
          }
          
          .receipt-logo {
            width: 40px !important;
            height: 40px !important;
            margin: 0 auto 5px auto;
          }
          
          .receipt-business-name {
            font-size: 16px !important;
            font-weight: bold !important;
            margin: 2px 0;
          }
          
          .receipt-business-info {
            font-size: 10px !important;
            margin: 1px 0;
          }
          
          .receipt-section {
            margin: 8px 0;
            padding: 4px 0;
          }
          
          .receipt-section-title {
            font-weight: bold !important;
            font-size: 11px !important;
            margin-bottom: 3px;
            text-transform: uppercase;
          }
          
          .receipt-order-info {
            border-bottom: 1px dashed #000;
            padding-bottom: 6px;
            margin-bottom: 8px;
          }
          
          .receipt-items {
            border-bottom: 1px dashed #000;
            padding-bottom: 6px;
            margin-bottom: 8px;
          }
          
          .receipt-item {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
          }
          
          .receipt-item-name {
            flex: 1;
            font-size: 11px !important;
            font-weight: bold !important;
          }
          
          .receipt-item-price {
            font-size: 11px !important;
            margin-left: 8px;
          }
          
          .receipt-item-options {
            font-size: 9px !important;
            color: #666 !important;
            margin-left: 8px;
            font-style: italic;
          }
          
          .receipt-totals {
            border-bottom: 1px dashed #000;
            padding-bottom: 6px;
            margin-bottom: 8px;
          }
          
          .receipt-total-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
            font-size: 11px !important;
          }
          
          .receipt-total-line.final {
            font-weight: bold !important;
            font-size: 13px !important;
            border-top: 1px solid #000;
            padding-top: 3px;
            margin-top: 4px;
          }
          
          .receipt-footer {
            text-align: center;
            font-size: 9px !important;
            margin-top: 8px;
          }
          
          .receipt-footer-line {
            margin: 2px 0;
          }
          
          .receipt-barcode {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 8px !important;
            margin: 5px 0;
            letter-spacing: 1px;
          }
          
          /* Hide non-print elements */
          .no-print {
            display: none !important;
          }
        }
        
        @media screen {
          .receipt-template {
            max-width: 320px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.3;
            color: #000;
          }
        }
      `}</style>

      {/* Receipt Header */}
      <div className="receipt-header">
        <div className="receipt-logo">
          <img src="/logo.svg" alt="Barcode Cafe" style={{ width: '100%', height: '100%' }} />
        </div>
        <div className="receipt-business-name">BARCODE CAFE</div>
        <div className="receipt-business-info">Standard Coffee House</div>
        <div className="receipt-business-info">123 Coffee Street, City Center</div>
        <div className="receipt-business-info">Tel: +965 1234 5678</div>
        <div className="receipt-business-info">www.barcodecafe.com</div>
      </div>

      {/* Order Information */}
      <div className="receipt-section receipt-order-info">
        <div className="receipt-section-title">
          {isClient ? t('receipt.orderInfo') : 'ORDER INFORMATION'}
        </div>
        <div style={{ fontSize: '10px' }}>
          <div>{isClient ? t('receipt.receiptNo') : 'Receipt No'}: {receiptNumber}</div>
          <div>{isClient ? t('receipt.orderNo') : 'Order No'}: #{order.id.slice(-8).toUpperCase()}</div>
          <div>{isClient ? t('receipt.date') : 'Date'}: {formatDate(order.createdAt, locale, true)}</div>
          <div>{isClient ? t('receipt.cashier') : 'Cashier'}: System</div>
          <div>{isClient ? t('receipt.paymentMethod') : 'Payment'}: {isClient ? t('checkout.cash') : 'Cash'}</div>
        </div>
      </div>

      {/* Customer Information */}
      {(order.deliveryType === 'DELIVERY' || order.customerName) && (
        <div className="receipt-section">
          <div className="receipt-section-title">
            {isClient ? t('receipt.customerInfo') : 'CUSTOMER INFORMATION'}
          </div>
          <div style={{ fontSize: '10px' }}>
            {order.customerName && (
              <div>{isClient ? t('receipt.customer') : 'Customer'}: {order.customerName}</div>
            )}
            {order.deliveryType === 'DELIVERY' && order.deliveryAddress && (
              <div>{isClient ? t('receipt.address') : 'Address'}: {order.deliveryAddress}</div>
            )}
            {order.deliveryType === 'TABLE' && order.tableNumber && (
              <div>{isClient ? t('receipt.table') : 'Table'}: {order.tableNumber}</div>
            )}
            {order.deliveryType === 'PICKUP' && (
              <div>{isClient ? t('receipt.pickup') : 'Pickup Order'}</div>
            )}
          </div>
        </div>
      )}

      {/* Order Items */}
      <div className="receipt-section receipt-items">
        <div className="receipt-section-title">
          {isClient ? t('receipt.items') : 'ORDER ITEMS'}
        </div>
        {order.items.map((item, index) => (
          <div key={index} className="receipt-item">
            <div style={{ flex: 1 }}>
              <div className="receipt-item-name">
                {item.quantity}x {item.name}
              </div>
              {item.options && item.options.length > 0 && (
                <div className="receipt-item-options">
                  {item.options.map((option, optIndex) => (
                    <div key={optIndex}>• {option.name}: {option.value}</div>
                  ))}
                </div>
              )}
            </div>
            <div className="receipt-item-price">
              {(item.price * item.quantity).toFixed(2)} {isClient ? t('common.currency') : 'SAR'}
            </div>
          </div>
        ))}
        
        {order.specialInstructions && (
          <div style={{ marginTop: '6px', fontSize: '10px', fontStyle: 'italic' }}>
            <strong>{isClient ? t('receipt.notes') : 'Notes'}:</strong> {order.specialInstructions}
          </div>
        )}
      </div>

      {/* Totals */}
      <div className="receipt-section receipt-totals">
        <div className="receipt-total-line">
          <span>{isClient ? t('receipt.subtotal') : 'Subtotal'}:</span>
          <span>{subtotal.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
        </div>
        
        {deliveryFee > 0 && (
          <div className="receipt-total-line">
            <span>{isClient ? t('receipt.deliveryFee') : 'Delivery Fee'}:</span>
            <span>{deliveryFee.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
          </div>
        )}
        
        {tax > 0 && (
          <div className="receipt-total-line">
            <span>{isClient ? t('receipt.tax') : 'Tax'}:</span>
            <span>{tax.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
          </div>
        )}
        
        <div className="receipt-total-line final">
          <span>{isClient ? t('receipt.total') : 'TOTAL'}:</span>
          <span>{total.toFixed(2)} {isClient ? t('common.currency') : 'SAR'}</span>
        </div>
      </div>

      {/* Receipt Footer */}
      <div className="receipt-footer">
        <div className="receipt-footer-line">
          <strong>{isClient ? t('receipt.thankYou') : 'Thank you for your visit!'}</strong>
        </div>
        <div className="receipt-footer-line">
          {isClient ? t('receipt.enjoyMeal') : 'Enjoy your meal and have a great day!'}
        </div>
        <div className="receipt-footer-line">
          {isClient ? t('receipt.followUs') : 'Follow us on social media @barcodecafe'}
        </div>
        
        {/* Receipt Barcode */}
        <div className="receipt-barcode">
          ||||| |||| | ||| |||| |||||
        </div>
        <div style={{ fontSize: '8px', marginTop: '2px' }}>
          {receiptNumber}
        </div>
        
        <div className="receipt-footer-line" style={{ marginTop: '8px', fontSize: '8px' }}>
          {isClient ? t('receipt.powered') : 'Powered by Barcode Cafe System'}
        </div>
      </div>
    </div>
  );
}
