"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";
import { updateOrder } from "@/lib/firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
}

export default function OrderDetailsModal({
  order,
  isOpen,
  onClose,
  onOrderUpdated
}: OrderDetailsModalProps) {
  const { t, isClient, locale } = useLocale();
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order?.status || OrderStatus.ORDER_PLACED);

  // Print receipt function
  const handlePrintReceipt = () => {
    if (!order) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const receiptNumber = `RC-${order.id.slice(-8).toUpperCase()}`;
    const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const deliveryFee = order.deliveryFee || 0;
    const tax = order.tax || 0;
    const total = order.total || (subtotal + deliveryFee + tax);

    const formatOrderDate = () => {
      if (order.createdAt instanceof Date) {
        return formatDate(order.createdAt, locale, true);
      } else if (typeof order.createdAt === 'string') {
        return formatDate(new Date(order.createdAt), locale, true);
      }
      return new Date().toLocaleDateString();
    };

    const receiptHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - ${receiptNumber}</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            width: 80mm;
            margin: 0;
            padding: 10px;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            background: white;
          }
          .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 8px;
          }
          .business-name {
            font-size: 16px;
            font-weight: bold;
            margin: 2px 0;
          }
          .business-info {
            font-size: 10px;
            margin: 1px 0;
          }
          .section {
            margin: 8px 0;
            padding: 4px 0;
          }
          .section-title {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 3px;
            text-transform: uppercase;
          }
          .order-info, .items, .totals {
            border-bottom: 1px dashed #000;
            padding-bottom: 6px;
            margin-bottom: 8px;
          }
          .item {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
          }
          .item-name {
            flex: 1;
            font-size: 11px;
            font-weight: bold;
          }
          .item-price {
            font-size: 11px;
            margin-left: 8px;
          }
          .item-options {
            font-size: 9px;
            color: #666;
            margin-left: 8px;
            font-style: italic;
          }
          .total-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
            font-size: 11px;
          }
          .total-line.final {
            font-weight: bold;
            font-size: 13px;
            border-top: 1px solid #000;
            padding-top: 3px;
            margin-top: 4px;
          }
          .footer {
            text-align: center;
            font-size: 9px;
            margin-top: 8px;
          }
          .footer-line {
            margin: 2px 0;
          }
          .barcode {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 8px;
            margin: 5px 0;
            letter-spacing: 1px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="business-name">BARCODE CAFE</div>
          <div class="business-info">Standard Coffee House</div>
          <div class="business-info">123 Coffee Street, City Center</div>
          <div class="business-info">Tel: +965 1234 5678</div>
          <div class="business-info">www.barcodecafe.com</div>
        </div>

        <div class="section order-info">
          <div class="section-title">${isClient ? t('receipt.orderInfo') : 'ORDER INFORMATION'}</div>
          <div style="font-size: 10px;">
            <div>${isClient ? t('receipt.receiptNo') : 'Receipt No'}: ${receiptNumber}</div>
            <div>${isClient ? t('receipt.orderNo') : 'Order No'}: #${order.id.slice(-8).toUpperCase()}</div>
            <div>${isClient ? t('receipt.date') : 'Date'}: ${formatOrderDate()}</div>
            <div>${isClient ? t('receipt.cashier') : 'Cashier'}: System</div>
            <div>${isClient ? t('receipt.paymentMethod') : 'Payment'}: ${isClient ? t('checkout.cash') : 'Cash'}</div>
          </div>
        </div>

        ${(order.deliveryType === 'DELIVERY' || order.customerName) ? `
        <div class="section">
          <div class="section-title">${isClient ? t('receipt.customerInfo') : 'CUSTOMER INFORMATION'}</div>
          <div style="font-size: 10px;">
            ${order.customerName ? `<div>${isClient ? t('receipt.customer') : 'Customer'}: ${order.customerName}</div>` : ''}
            ${order.deliveryType === 'DELIVERY' && order.deliveryAddress ? `<div>${isClient ? t('receipt.address') : 'Address'}: ${order.deliveryAddress}</div>` : ''}
            ${order.deliveryType === 'TABLE' && order.tableNumber ? `<div>${isClient ? t('receipt.table') : 'Table'}: ${order.tableNumber}</div>` : ''}
            ${order.deliveryType === 'PICKUP' ? `<div>${isClient ? t('receipt.pickup') : 'Pickup Order'}</div>` : ''}
          </div>
        </div>
        ` : ''}

        <div class="section items">
          <div class="section-title">${isClient ? t('receipt.items') : 'ORDER ITEMS'}</div>
          ${order.items.map(item => `
            <div class="item">
              <div style="flex: 1;">
                <div class="item-name">${item.quantity}x ${item.name}</div>
                ${item.options && item.options.length > 0 ? `
                  <div class="item-options">
                    ${item.options.map(option => `• ${option.name}: ${option.value}`).join('<br>')}
                  </div>
                ` : ''}
              </div>
              <div class="item-price">
                ${(item.price * item.quantity).toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}
              </div>
            </div>
          `).join('')}

          ${order.specialInstructions ? `
            <div style="margin-top: 6px; font-size: 10px; font-style: italic;">
              <strong>${isClient ? t('receipt.notes') : 'Notes'}:</strong> ${order.specialInstructions}
            </div>
          ` : ''}
        </div>

        <div class="section totals">
          <div class="total-line">
            <span>${isClient ? t('receipt.subtotal') : 'Subtotal'}:</span>
            <span>${subtotal.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>

          ${deliveryFee > 0 ? `
          <div class="total-line">
            <span>${isClient ? t('receipt.deliveryFee') : 'Delivery Fee'}:</span>
            <span>${deliveryFee.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
          ` : ''}

          ${tax > 0 ? `
          <div class="total-line">
            <span>${isClient ? t('receipt.tax') : 'Tax'}:</span>
            <span>${tax.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
          ` : ''}

          <div class="total-line final">
            <span>${isClient ? t('receipt.total') : 'TOTAL'}:</span>
            <span>${total.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
        </div>

        <div class="footer">
          <div class="footer-line">
            <strong>${isClient ? t('receipt.thankYou') : 'Thank you for your visit!'}</strong>
          </div>
          <div class="footer-line">
            ${isClient ? t('receipt.enjoyMeal') : 'Enjoy your meal and have a great day!'}
          </div>
          <div class="footer-line">
            ${isClient ? t('receipt.followUs') : 'Follow us on social media @barcodecafe'}
          </div>

          <div class="barcode">
            ||||| |||| | ||| |||| |||||
          </div>
          <div style="font-size: 8px; margin-top: 2px;">
            ${receiptNumber}
          </div>

          <div class="footer-line" style="margin-top: 8px; font-size: 8px;">
            ${isClient ? t('receipt.powered') : 'Powered by Barcode Cafe System'}
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(receiptHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  // Update selected status when order changes
  useEffect(() => {
    if (order?.status) {
      setSelectedStatus(order.status);
    }
  }, [order?.status]);

  if (!order) return null;

  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return isClient ? t("orders.status.orderPlaced") : "Order Placed";
      case OrderStatus.PREPARING:
        return isClient ? t("orders.status.preparing") : "Preparing";
      case OrderStatus.READY_FOR_PICKUP:
        return isClient ? t("orders.status.readyForPickup") : "Ready for Pickup";
      case OrderStatus.OUT_FOR_DELIVERY:
        return isClient ? t("orders.status.outForDelivery") : "Out for Delivery";
      case OrderStatus.DELIVERED:
        return isClient ? t("orders.status.delivered") : "Delivered";
      case OrderStatus.CANCELLED:
        return isClient ? t("orders.status.cancelled") : "Cancelled";
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
      case OrderStatus.DELIVERED:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return isClient ? t("orders.paymentMethods.creditCard") : "Credit Card";
      case PaymentMethod.DEBIT_CARD:
        return isClient ? t("orders.paymentMethods.debitCard") : "Debit Card";
      case PaymentMethod.CASH:
        return isClient ? t("orders.paymentMethods.cash") : "Cash";
      case PaymentMethod.GIFT_CARD:
        return isClient ? t("orders.paymentMethods.giftCard") : "Gift Card";
      case PaymentMethod.LOYALTY_POINTS:
        return isClient ? t("orders.paymentMethods.loyaltyPoints") : "Loyalty Points";
      default:
        return method;
    }
  };

  const handleStatusUpdate = async () => {
    if (selectedStatus === order.status) return;

    setIsUpdating(true);
    try {
      const updatedOrder = await updateOrder(order.id, {
        status: selectedStatus,
        updatedAt: new Date()
      });
      
      onOrderUpdated(updatedOrder);
      onClose();
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            {isClient ? t("orders.orderDetails") : "Order Details"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Header */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isClient ? t("orders.orderNumber") : "Order Number"}: #{order.id.slice(-8).toUpperCase()}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {order.createdAt instanceof Date 
                  ? formatDate(order.createdAt, true) 
                  : typeof order.createdAt === 'string' 
                    ? formatDate(new Date(order.createdAt), true)
                    : ''}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusBadgeClass(order.status)}`}>
                {getOrderStatusLabel(order.status)}
              </span>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4 dark:text-gray-100">
              {isClient ? t("orders.items") : "Order Items"}
            </h3>
            <div className="space-y-3">
              {(order.items || []).map((item, index) => (
                <div key={index} className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="font-medium dark:text-gray-100">
                      {item.quantity || 0}x {item.name || 'Unknown Item'}
                    </p>
                    {item.options && item.options.length > 0 && (
                      <ul className="mt-1 space-y-1">
                        {item.options.map((option, optIndex) => (
                          <li key={optIndex} className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <span className="w-4 h-0 border-t border-gray-300 dark:border-gray-600 mr-2"></span>
                            {option.name || 'Option'}: {option.value || 'N/A'}
                            {(option.priceAdjustment || 0) !== 0 && (
                              <span className="ml-1">
                                ({(option.priceAdjustment || 0) > 0 ? '+' : ''}
                                {isClient ? t('common.currency') : 'SAR'} {(option.priceAdjustment || 0).toFixed(2)})
                              </span>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-medium dark:text-gray-100">
                      {isClient ? t('common.currency') : 'SAR'} {((item.price || 0) * (item.quantity || 0)).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Delivery Information */}
          {(order.deliveryAddress || order.tableNumber) && (
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
                {isClient ? t("admin.deliveryInfo") : "Delivery Information"}
              </h3>
              {order.deliveryAddress && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{isClient ? t("checkout.deliveryAddress") : "Address"}:</span> {order.deliveryAddress}
                </p>
              )}
              {order.tableNumber && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{isClient ? t("checkout.tableNumber") : "Table"}:</span> {order.tableNumber}
                </p>
              )}
            </div>
          )}

          {/* Special Instructions */}
          {order.specialInstructions && (
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
                {isClient ? t("orders.specialInstructions") : "Special Instructions"}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {order.specialInstructions}
              </p>
            </div>
          )}

          {/* Order Summary */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
              {isClient ? t("orders.summary") : "Order Summary"}
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">{isClient ? t("orders.subtotal") : "Subtotal"}</span>
                <span className="dark:text-gray-100">
                  {isClient ? t('common.currency') : 'SAR'} {(order.subtotal || 0).toFixed(2)}
                </span>
              </div>
              {(order.deliveryFee || 0) > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">{isClient ? t("checkout.deliveryFee") : "Delivery Fee"}</span>
                  <span className="dark:text-gray-100">
                    {isClient ? t('common.currency') : 'SAR'} {(order.deliveryFee || 0).toFixed(2)}
                  </span>
                </div>
              )}
              <div className="flex justify-between font-medium pt-2 border-t border-gray-200 dark:border-gray-600">
                <span className="dark:text-gray-100">{isClient ? t("orders.total") : "Total"}</span>
                <span className="dark:text-gray-100">
                  {isClient ? t('common.currency') : 'SAR'} {(order.total || 0).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between text-sm pt-2">
                <span className="text-gray-600 dark:text-gray-400">{isClient ? t("orders.paymentMethod") : "Payment Method"}</span>
                <span className="dark:text-gray-100">{getPaymentMethodLabel(order.paymentMethod)}</span>
              </div>
            </div>
          </div>

          {/* Status Update */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
              {isClient ? t("admin.updateStatus") : "Update Order Status"}
            </h3>
            <div className="flex items-center gap-3">
              <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as OrderStatus)}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={OrderStatus.ORDER_PLACED}>{getOrderStatusLabel(OrderStatus.ORDER_PLACED)}</SelectItem>
                  <SelectItem value={OrderStatus.PREPARING}>{getOrderStatusLabel(OrderStatus.PREPARING)}</SelectItem>
                  <SelectItem value={OrderStatus.READY_FOR_PICKUP}>{getOrderStatusLabel(OrderStatus.READY_FOR_PICKUP)}</SelectItem>
                  <SelectItem value={OrderStatus.OUT_FOR_DELIVERY}>{getOrderStatusLabel(OrderStatus.OUT_FOR_DELIVERY)}</SelectItem>
                  <SelectItem value={OrderStatus.DELIVERED}>{getOrderStatusLabel(OrderStatus.DELIVERED)}</SelectItem>
                  <SelectItem value={OrderStatus.CANCELLED}>{getOrderStatusLabel(OrderStatus.CANCELLED)}</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                onClick={handleStatusUpdate}
                disabled={isUpdating || selectedStatus === order.status}
                className="bg-[#56999B] hover:bg-[#74C8CA] text-white"
              >
                {isUpdating ? (
                  <>
                    <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                    {isClient ? t("common.updating") : "Updating..."}
                  </>
                ) : (
                  <>
                    <i className="fa-solid fa-save mr-2"></i>
                    {isClient ? t("admin.updateStatus") : "Update Status"}
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
            <Button variant="outline" onClick={onClose}>
              {isClient ? t("common.close") : "Close"}
            </Button>
            <Button
              onClick={handlePrintReceipt}
              className="bg-[#83EAED] hover:bg-[#83EAED]/90 text-white"
            >
              <i className="fa-solid fa-print mr-2"></i>
              {isClient ? t("orders.printReceipt") : "Print Receipt"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
