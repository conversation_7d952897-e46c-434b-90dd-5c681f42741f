"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import { getOrder } from "@/lib/firebase/firestore";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";

export default function OrderDetailsPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const orderId = params?.id as string;
  const router = useRouter();
  const { t, isClient, locale } = useLocale();
  const { user, loading } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Print receipt function
  const handlePrintReceipt = () => {
    if (!order) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const receiptNumber = `RC-${order.id.slice(-8).toUpperCase()}`;
    const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const deliveryFee = order.deliveryFee || 0;
    const tax = order.tax || 0;
    const total = order.total || (subtotal + deliveryFee + tax);

    const formatOrderDate = () => {
      if (order.createdAt instanceof Date) {
        return formatDate(order.createdAt, locale, true);
      } else if (typeof order.createdAt === 'string') {
        return formatDate(new Date(order.createdAt), locale, true);
      }
      return new Date().toLocaleDateString();
    };

    const receiptHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - ${receiptNumber}</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            width: 80mm;
            margin: 0;
            padding: 10px;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            background: white;
          }
          .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 8px;
          }
          .business-name {
            font-size: 16px;
            font-weight: bold;
            margin: 2px 0;
          }
          .business-info {
            font-size: 10px;
            margin: 1px 0;
          }
          .section {
            margin: 8px 0;
            padding: 4px 0;
          }
          .section-title {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 3px;
            text-transform: uppercase;
          }
          .order-info, .items, .totals {
            border-bottom: 1px dashed #000;
            padding-bottom: 6px;
            margin-bottom: 8px;
          }
          .item {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
          }
          .item-name {
            flex: 1;
            font-size: 11px;
            font-weight: bold;
          }
          .item-price {
            font-size: 11px;
            margin-left: 8px;
          }
          .item-options {
            font-size: 9px;
            color: #666;
            margin-left: 8px;
            font-style: italic;
          }
          .total-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
            font-size: 11px;
          }
          .total-line.final {
            font-weight: bold;
            font-size: 13px;
            border-top: 1px solid #000;
            padding-top: 3px;
            margin-top: 4px;
          }
          .footer {
            text-align: center;
            font-size: 9px;
            margin-top: 8px;
          }
          .footer-line {
            margin: 2px 0;
          }
          .barcode {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 8px;
            margin: 5px 0;
            letter-spacing: 1px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="business-name">BARCODE CAFE</div>
          <div class="business-info">Standard Coffee House</div>
          <div class="business-info">123 Coffee Street, City Center</div>
          <div class="business-info">Tel: +965 1234 5678</div>
          <div class="business-info">www.barcodecafe.com</div>
        </div>

        <div class="section order-info">
          <div class="section-title">${isClient ? t('receipt.orderInfo') : 'ORDER INFORMATION'}</div>
          <div style="font-size: 10px;">
            <div>${isClient ? t('receipt.receiptNo') : 'Receipt No'}: ${receiptNumber}</div>
            <div>${isClient ? t('receipt.orderNo') : 'Order No'}: #${order.id.slice(-8).toUpperCase()}</div>
            <div>${isClient ? t('receipt.date') : 'Date'}: ${formatOrderDate()}</div>
            <div>${isClient ? t('receipt.cashier') : 'Cashier'}: System</div>
            <div>${isClient ? t('receipt.paymentMethod') : 'Payment'}: ${isClient ? t('checkout.cash') : 'Cash'}</div>
          </div>
        </div>

        ${(order.deliveryType === 'DELIVERY' || order.customerName) ? `
        <div class="section">
          <div class="section-title">${isClient ? t('receipt.customerInfo') : 'CUSTOMER INFORMATION'}</div>
          <div style="font-size: 10px;">
            ${order.customerName ? `<div>${isClient ? t('receipt.customer') : 'Customer'}: ${order.customerName}</div>` : ''}
            ${order.deliveryType === 'DELIVERY' && order.deliveryAddress ? `<div>${isClient ? t('receipt.address') : 'Address'}: ${order.deliveryAddress}</div>` : ''}
            ${order.deliveryType === 'TABLE' && order.tableNumber ? `<div>${isClient ? t('receipt.table') : 'Table'}: ${order.tableNumber}</div>` : ''}
            ${order.deliveryType === 'PICKUP' ? `<div>${isClient ? t('receipt.pickup') : 'Pickup Order'}</div>` : ''}
          </div>
        </div>
        ` : ''}

        <div class="section items">
          <div class="section-title">${isClient ? t('receipt.items') : 'ORDER ITEMS'}</div>
          ${order.items.map(item => `
            <div class="item">
              <div style="flex: 1;">
                <div class="item-name">${item.quantity}x ${item.name}</div>
                ${item.options && item.options.length > 0 ? `
                  <div class="item-options">
                    ${item.options.map(option => `• ${option.name}: ${option.value}`).join('<br>')}
                  </div>
                ` : ''}
              </div>
              <div class="item-price">
                ${(item.price * item.quantity).toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}
              </div>
            </div>
          `).join('')}

          ${order.specialInstructions ? `
            <div style="margin-top: 6px; font-size: 10px; font-style: italic;">
              <strong>${isClient ? t('receipt.notes') : 'Notes'}:</strong> ${order.specialInstructions}
            </div>
          ` : ''}
        </div>

        <div class="section totals">
          <div class="total-line">
            <span>${isClient ? t('receipt.subtotal') : 'Subtotal'}:</span>
            <span>${subtotal.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>

          ${deliveryFee > 0 ? `
          <div class="total-line">
            <span>${isClient ? t('receipt.deliveryFee') : 'Delivery Fee'}:</span>
            <span>${deliveryFee.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
          ` : ''}

          ${tax > 0 ? `
          <div class="total-line">
            <span>${isClient ? t('receipt.tax') : 'Tax'}:</span>
            <span>${tax.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
          ` : ''}

          <div class="total-line final">
            <span>${isClient ? t('receipt.total') : 'TOTAL'}:</span>
            <span>${total.toFixed(2)} ${isClient ? t('common.currency') : 'SAR'}</span>
          </div>
        </div>

        <div class="footer">
          <div class="footer-line">
            <strong>${isClient ? t('receipt.thankYou') : 'Thank you for your visit!'}</strong>
          </div>
          <div class="footer-line">
            ${isClient ? t('receipt.enjoyMeal') : 'Enjoy your meal and have a great day!'}
          </div>
          <div class="footer-line">
            ${isClient ? t('receipt.followUs') : 'Follow us on social media @barcodecafe'}
          </div>

          <div class="barcode">
            ||||| |||| | ||| |||| |||||
          </div>
          <div style="font-size: 8px; margin-top: 2px;">
            ${receiptNumber}
          </div>

          <div class="footer-line" style="margin-top: 8px; font-size: 8px;">
            ${isClient ? t('receipt.powered') : 'Powered by Barcode Cafe System'}
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(receiptHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
  
  // Load order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (user?.uid && orderId) {
        setIsLoading(true);
        try {
          const orderData = await getOrder(orderId);
          
          // Verify that the order belongs to the current user
          if (orderData && orderData.userId === user.uid) {
            setOrder(orderData);
          } else {
            // Order not found or doesn't belong to this user
            router.replace("/customer/orders");
          }
        } catch (error) {
          console.error("Error fetching order details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchOrderDetails();
    }
  }, [user, orderId, router]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !user.emailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return t("orders.paymentMethods.creditCard");
      case PaymentMethod.DEBIT_CARD:
        return t("orders.paymentMethods.debitCard");
      case PaymentMethod.CASH:
        return t("orders.paymentMethods.cash");
      case PaymentMethod.GIFT_CARD:
        return t("orders.paymentMethods.giftCard");
      case PaymentMethod.LOYALTY_POINTS:
        return t("orders.paymentMethods.loyaltyPoints");
      default:
        return method;
    }
  };

  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return t("orders.status.orderPlaced");
      case OrderStatus.PREPARING:
        return t("orders.status.preparing");
      case OrderStatus.READY_FOR_PICKUP:
        return t("orders.status.readyForPickup");
      case OrderStatus.OUT_FOR_DELIVERY:
        return t("orders.status.outForDelivery");
      case OrderStatus.DELIVERED:
        return t("orders.status.delivered");
      case OrderStatus.CANCELLED:
        return t("orders.status.cancelled");
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
      case OrderStatus.DELIVERED:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold dark:text-gray-100">
              {t("orders.orderDetails")}
            </h1>
            {order && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t("orders.orderNumber")}: #{order.id.slice(-8).toUpperCase()}
              </p>
            )}
          </div>
          <button
            onClick={() => router.back()}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <i className="fa-solid fa-arrow-left mr-2"></i>
            {t("common.back")}
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : order ? (
          <div className="p-6">
            {/* Order Status and Date */}
            <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8">
              <div className="mb-4 md:mb-0">
                <span className={`text-sm px-3 py-1 rounded-full ${getStatusBadgeClass(order.status)}`}>
                  {getOrderStatusLabel(order.status)}
                </span>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {order.createdAt instanceof Date 
                    ? formatDate(order.createdAt, true) 
                    : typeof order.createdAt === 'string' 
                      ? formatDate(new Date(order.createdAt), true)
                      : ''}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("orders.paymentMethod")}
                </p>
                <p className="font-medium dark:text-gray-100">
                  {getPaymentMethodLabel(order.paymentMethod)}
                </p>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium mb-4 dark:text-gray-100">
                {t("orders.items")}
              </h2>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {order.items.map((item, index) => (
                  <div key={index} className="py-4 flex justify-between">
                    <div>
                      <p className="font-medium dark:text-gray-100">
                        {item.quantity}x {item.name}
                      </p>
                      {item.options && item.options.length > 0 && (
                        <ul className="mt-1 space-y-1">
                          {item.options.map((option, optIndex) => (
                            <li key={optIndex} className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                              <span className="w-4 h-0 border-t border-gray-300 dark:border-gray-600 mr-2"></span>
                              {option.name}: {option.value}
                              {option.priceAdjustment > 0 && (
                                <span className="ml-1">
                                  (+{isClient ? `${t('common.currency')} ${option.priceAdjustment.toFixed(2)}` : `SAR ${option.priceAdjustment.toFixed(2)}`})
                                </span>
                              )}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                    <p className="font-medium dark:text-gray-100">
                      {isClient ? `${t('common.currency')} ${(item.price * item.quantity).toFixed(2)}` : `SAR ${(item.price * item.quantity).toFixed(2)}`}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Special Instructions */}
            {order.specialInstructions && (
              <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6 mb-6">
                <h2 className="text-lg font-medium mb-2 dark:text-gray-100">
                  {t("orders.specialInstructions")}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  {order.specialInstructions}
                </p>
              </div>
            )}

            {/* Order Summary */}
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4 dark:text-gray-100">
                {t("orders.summary")}
              </h2>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t("orders.subtotal")}</span>
                  <span className="dark:text-gray-100">
                    {isClient ? `${t('common.currency')} ${(order.total * 0.85).toFixed(2)}` : `SAR ${(order.total * 0.85).toFixed(2)}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t("orders.tax")} (15%)</span>
                  <span className="dark:text-gray-100">
                    {isClient ? `${t('common.currency')} ${(order.total * 0.15).toFixed(2)}` : `SAR ${(order.total * 0.15).toFixed(2)}`}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                  <div className="flex justify-between font-medium">
                    <span className="dark:text-gray-100">{t("orders.total")}</span>
                    <span className="dark:text-gray-100">
                      {isClient ? `${t('common.currency')} ${order.total.toFixed(2)}` : `SAR ${order.total.toFixed(2)}`}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Actions */}
            <div className="mt-8 flex justify-end">
              {order.status === OrderStatus.ORDER_PLACED && (
                <button
                  className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500 px-4 py-2 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50"
                >
                  {t("orders.cancelOrder")}
                </button>
              )}
              <button
                className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] ml-4"
                onClick={handlePrintReceipt}
              >
                <i className="fa-solid fa-print mr-2"></i>
                {t("orders.printReceipt")}
              </button>
            </div>

          </div>
        ) : (
          <div className="p-16 text-center">
            <i className="fa-solid fa-circle-exclamation text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
            <h2 className="text-xl font-medium mb-2 dark:text-gray-100">
              {t("orders.notFound.title")}
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {t("orders.notFound.message")}
            </p>
            <button 
              className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0]"
              onClick={() => router.push("/customer/orders")}
            >
              {t("orders.backToOrders")}
            </button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
